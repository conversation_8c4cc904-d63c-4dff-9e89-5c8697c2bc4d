{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}, {"datasource": {"type": "prometheus"}, "enable": true, "expr": "sum(changes(nginx_ingress_controller_config_last_reload_successful_timestamp_seconds{instance!=\"unknown\",controller_class=~\"$controller_class\",namespace=~\"$namespace\"}[30s])) by (controller_class)", "hide": false, "iconColor": "rgba(255, 96, 96, 1)", "limit": 100, "name": "Config Reloads", "showIn": 0, "step": "30s", "tagKeys": "controller_class", "tags": [], "titleFormat": "Config Reloaded", "type": "tags"}]}, "description": "Ingress-nginx supports a rich collection of prometheus metrics. If you have prometheus and grafana installed on your cluster then prometheus will already be scraping this data due to the scrape annotation on the deployment.", "editable": true, "gnetId": 9614, "graphTooltip": 0, "id": 791, "iteration": 1635879198567, "links": [], "panels": [{"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "id": 23, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"bo-audit-log\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"bo-audit-log\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "bo-audit-log", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 0}, "id": 24, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-accounting-common\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-accounting-common\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-accounting-common", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 0}, "id": 25, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-accounting-processor\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-accounting-processor\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-accounting-processor", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 0}, "id": 26, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-accounting-sport\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-accounting-sport\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-accounting-sport", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 3}, "id": 28, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-ams\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-ams\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-ams", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 3}, "id": 31, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-back-office\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-back-office\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-back-office", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 3}, "id": 32, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-background-scheduler\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-background-scheduler\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-background-scheduler", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 3}, "id": 33, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-betting\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-betting\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-betting", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 6}, "id": 27, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-cms\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-cms\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-cms", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 6}, "id": 35, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-combinator\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-combinator\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-combinator", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 6}, "id": 29, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-crm\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-crm\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-crm", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 6}, "id": 34, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-egtdemo\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-egtdemo\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-egtdemo", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 9}, "id": 30, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-error-collector\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-error-collector\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-error-collector", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 9}, "id": 39, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-excel-reporting\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-excel-reporting\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-excel-reporting", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 9}, "id": 37, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-file-service\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-file-service\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-file-service", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 9}, "id": 38, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-gaming\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-gaming\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-gaming", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 12}, "id": 36, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-gaming-accounting\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-gaming-accounting\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-gaming-accounting", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 12}, "id": 43, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-inbet\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-inbet\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-inbet", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 12}, "id": 41, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-liability-api\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-liability-api\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-liability-api", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 12}, "id": 42, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-liability-service\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-liability-service\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-liability-service", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 15}, "id": 40, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-notification-service\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-notification-service\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-notification-service", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 15}, "id": 46, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-oauth\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-oauth\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-oauth", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 15}, "id": 48, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-payment\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-payment\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-payment", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 15}, "id": 50, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-reporting\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-reporting\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-reporting", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 18}, "id": 44, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-sport-events-api\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-sport-events-api\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-sport-events-api", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 18}, "id": 51, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-today\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-today\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-today", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 18}, "id": 47, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-tournaments-api\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-tournaments-api\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-tournaments-api", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 18}, "id": 49, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-trading\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-trading\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-trading", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 21}, "id": 45, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-vermantia\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-vermantia\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-vermantia", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 21}, "id": 55, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-websocket-mgnt\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-websocket-mgnt\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-websocket-mgnt", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 21}, "id": 53, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-websocket-mgnt-net\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-websocket-mgnt-net\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-websocket-mgnt-net", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 21}, "id": 54, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-winbet\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-winbet\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-winbet", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 24}, "id": 52, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-winbet-ro\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-winbet-ro\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-winbet-ro", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 24}, "id": 56, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-winbet-rs\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-winbet-rs\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-winbet-rs", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 24}, "id": 57, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-winbet-tz\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-winbet-tz\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-winbet-tz", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 24}, "id": 58, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-winbet-ua\",status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_requests{exported_service=\"ews-winbet-ua\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ews-winbet-ua", "type": "stat"}], "refresh": "", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": ".*", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus"}, "definition": "", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "Namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(nginx_ingress_controller_config_hash, controller_namespace)", "refId": "Prometheus-namespace-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus"}, "definition": "", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "Controller Class", "multi": false, "name": "controller_class", "options": [], "query": {"query": "label_values(nginx_ingress_controller_config_hash{namespace=~\"$namespace\"}, controller_class) ", "refId": "Prometheus-controller_class-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus"}, "definition": "", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "Controller", "multi": false, "name": "controller", "options": [], "query": {"query": "label_values(nginx_ingress_controller_config_hash{namespace=~\"$namespace\",controller_class=~\"$controller_class\"}, controller_pod) ", "refId": "Prometheus-controller-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus"}, "definition": "", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "Ingress", "multi": false, "name": "ingress", "options": [], "query": {"query": "label_values(nginx_ingress_controller_requests{namespace=~\"$namespace\",controller_class=~\"$controller_class\",controller=~\"$controller\"}, ingress) ", "refId": "Prometheus-ingress-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "2m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "NGINX Ingress controller Success rate", "uid": "fyScLaFnk", "version": 11}