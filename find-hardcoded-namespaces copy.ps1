#!/usr/bin/env pwsh

# Script to find dashboard.json files with hardcoded namespace values in queries
# Looks for namespace="some-value" where some-value is not $namespace

Write-Host "Searching for dashboard.json files with hardcoded namespace values..." -ForegroundColor Yellow
Write-Host ""

$foundFiles = @()
$totalFiles = 0
$debugMode = $true

# Get all dashboard.json files recursively
$dashboardFiles = Get-ChildItem -Path . -Name "dashboard.json" -Recurse

foreach ($file in $dashboardFiles) {
    $totalFiles++
    $fullPath = $file
    
    try {
        $content = Get-Content $fullPath -Raw -ErrorAction Stop
        
        # Look for hardcoded namespace patterns in Prometheus queries
        # Pattern: namespace=\"literal-value\" (escaped quotes in JSON)
        $hardcodedMatches = @()

        # Find all namespace=\"...\" patterns (escaped quotes in JSON)
        $namespaceMatches = [regex]::Matches($content, 'namespace=\\"([^"]+)\\"')

        foreach ($match in $namespaceMatches) {
            $namespaceValue = $match.Groups[1].Value

            # Skip if it's a variable (starts with $)
            # Skip specific patterns we want to exclude
            $excludePatterns = @('ews-${environment}', 'infrastructure', '([^"')
            if (-not $namespaceValue.StartsWith('$') -and $namespaceValue -notin $excludePatterns) {
                $hardcodedMatches += $match.Value
            }
        }

        # Also look for namespace=~\"literal-value\" patterns (regex matching with escaped quotes)
        $regexNamespaceMatches = [regex]::Matches($content, 'namespace=~\\"([^"]+)\\"')

        foreach ($match in $regexNamespaceMatches) {
            $namespaceValue = $match.Groups[1].Value

            # Skip if it's a variable pattern like ${namespace} or contains $
            # Skip specific regex patterns we want to exclude
            $excludeRegexPatterns = @('infrastructure', '.+', '.*')
            if (-not ($namespaceValue.Contains('$') -or $namespaceValue.Contains('{')) -and $namespaceValue -notin $excludeRegexPatterns) {
                $hardcodedMatches += $match.Value
            }
        }

        # Also check for unescaped quotes (in case some files have them)
        $unescapedMatches = [regex]::Matches($content, 'namespace="([^"]+)"')

        foreach ($match in $unescapedMatches) {
            $namespaceValue = $match.Groups[1].Value

            # Skip if it's a variable (starts with $)
            # Skip specific patterns we want to exclude
            $excludePatterns = @('ews-${environment}', 'infrastructure', '([^"')
            if (-not $namespaceValue.StartsWith('$') -and $namespaceValue -notin $excludePatterns) {
                $hardcodedMatches += $match.Value
            }
        }
        
        if ($hardcodedMatches.Count -gt 0) {
            $foundFiles += [PSCustomObject]@{
                File = $fullPath
                HardcodedNamespaces = $hardcodedMatches
            }
            
            Write-Host "Found hardcoded namespace(s) in: $fullPath" -ForegroundColor Red
            foreach ($match in $hardcodedMatches) {
                Write-Host "  - $match" -ForegroundColor White
            }
            Write-Host ""
        }
    }
    catch {
        Write-Warning "Error reading file $fullPath`: $_"
    }
}

Write-Host "Summary:" -ForegroundColor Cyan
Write-Host "Total dashboard.json files processed: $totalFiles"
Write-Host "Files with hardcoded namespaces: $($foundFiles.Count)"

if ($foundFiles.Count -gt 0) {
    Write-Host ""
    Write-Host "Files that need to be updated:" -ForegroundColor Yellow
    foreach ($file in $foundFiles) {
        Write-Host "- $($file.File)" -ForegroundColor White
    }
}
