﻿{
    "annotations":  {
                        "list":  [
                                     {
                                         "builtIn":  1,
                                         "datasource":  "-- <PERSON>ana --",
                                         "enable":  true,
                                         "hide":  true,
                                         "iconColor":  "rgba(0, 211, 255, 1)",
                                         "name":  "Annotations \u0026 Alerts",
                                         "type":  "dashboard"
                                     }
                                 ]
                    },
    "editable":  true,
    "gnetId":  null,
    "graphTooltip":  0,
    "id":  1255,
    "links":  [

              ],
    "panels":  [
                   {
                       "aliasColors":  {

                                       },
                       "bars":  false,
                       "dashLength":  10,
                       "dashes":  false,
                       "datasource":  null,
                       "fieldConfig":  {
                                           "defaults":  {
                                                            "custom":  {

                                                                       }
                                                        },
                                           "overrides":  [

                                                         ]
                                       },
                       "fill":  1,
                       "fillGradient":  0,
                       "gridPos":  {
                                       "h":  22,
                                       "w":  24,
                                       "x":  0,
                                       "y":  0
                                   },
                       "hiddenSeries":  false,
                       "id":  4,
                       "legend":  {
                                      "avg":  false,
                                      "current":  false,
                                      "max":  false,
                                      "min":  false,
                                      "show":  true,
                                      "total":  false,
                                      "values":  false
                                  },
                       "lines":  true,
                       "linewidth":  1,
                       "nullPointMode":  "null",
                       "options":  {
                                       "alertThreshold":  true
                                   },
                       "percentage":  false,
                       "pluginVersion":  "7.4.2",
                       "pointradius":  2,
                       "points":  false,
                       "renderer":  "flot",
                       "seriesOverrides":  [

                                           ],
                       "spaceLength":  10,
                       "stack":  false,
                       "steppedLine":  false,
                       "targets":  [
                                       {
                                           "expr":  "pg_replication_lag{namespace=\"$namespace\"}",
                                           "interval":  "",
                                           "legendFormat":  "",
                                           "refId":  "A"
                                       }
                                   ],
                       "thresholds":  [

                                      ],
                       "timeFrom":  null,
                       "timeRegions":  [

                                       ],
                       "timeShift":  null,
                       "title":  "Delay Replica",
                       "tooltip":  {
                                       "shared":  true,
                                       "sort":  0,
                                       "value_type":  "individual"
                                   },
                       "type":  "graph",
                       "xaxis":  {
                                     "buckets":  null,
                                     "mode":  "time",
                                     "name":  null,
                                     "show":  true,
                                     "values":  [

                                                ]
                                 },
                       "yaxes":  [
                                     {
                                         "$$hashKey":  "object:730",
                                         "format":  "s",
                                         "label":  null,
                                         "logBase":  1,
                                         "max":  null,
                                         "min":  null,
                                         "show":  true
                                     },
                                     {
                                         "$$hashKey":  "object:731",
                                         "format":  "short",
                                         "label":  null,
                                         "logBase":  1,
                                         "max":  null,
                                         "min":  null,
                                         "show":  true
                                     }
                                 ],
                       "yaxis":  {
                                     "align":  false,
                                     "alignLevel":  null
                                 }
                   }
               ],
    "schemaVersion":  27,
    "style":  "dark",
    "tags":  [

             ],
    "templating":  {
                       "list":  [
                                    {
                                        "definition":  "",
                                        "description":  "Environment namespace",
                                        "type":  "custom",
                                        "name":  "namespace",
                                        "datasource":  null,
                                        "allValue":  null,
                                        "tagsQuery":  "",
                                        "useTags":  false,
                                        "label":  "Namespace",
                                        "tagValuesQuery":  "",
                                        "query":  "",
                                        "regex":  "",
                                        "sort":  0,
                                        "current":  {
                                                        "selected":  false,
                                                        "value":  "ews-sof-prod",
                                                        "text":  "ews-sof-prod"
                                                    },
                                        "options":  [
                                                        {
                                                            "selected":  true,
                                                            "value":  "ews-sof-prod",
                                                            "text":  "ews-sof-prod"
                                                        },
                                                        {
                                                            "selected":  false,
                                                            "value":  "ews-dev",
                                                            "text":  "ews-dev"
                                                        },
                                                        {
                                                            "selected":  false,
                                                            "value":  "ews-uat",
                                                            "text":  "ews-uat"
                                                        },
                                                        {
                                                            "selected":  false,
                                                            "value":  "ews-int",
                                                            "text":  "ews-int"
                                                        }
                                                    ],
                                        "tags":  [

                                                 ],
                                        "refresh":  0,
                                        "error":  null,
                                        "skipUrlSync":  false,
                                        "hide":  0,
                                        "multi":  false,
                                        "includeAll":  false
                                    }
                                ]
                   },
    "time":  {
                 "from":  "now-3h",
                 "to":  "now"
             },
    "timepicker":  {

                   },
    "timezone":  "",
    "title":  "Delay",
    "uid":  "RdsVLwY7z",
    "version":  1
}