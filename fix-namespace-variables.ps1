#!/usr/bin/env pwsh

# Script to add namespace variables to Grafana dashboard JSON files and replace hardcoded namespace values
# Usage: .\fix-namespace-variables.ps1 [-DryRun] [-FilePath "path/to/dashboard.json"]

param(
    [switch]$DryRun = $false,
    [string]$FilePath = ""
)

function Add-NamespaceVariable {
    param(
        [string]$JsonContent
    )

    # Define the namespace variable JSON string with proper formatting
    $namespaceVariableJson = @'
    {
      "allValue": null,
      "current": {
        "selected": false,
        "text": "ews-sof-prod",
        "value": "ews-sof-prod"
      },
      "datasource": null,
      "definition": "",
      "description": "Environment namespace",
      "error": null,
      "hide": 0,
      "includeAll": false,
      "label": "Namespace",
      "multi": false,
      "name": "namespace",
      "options": [
        {
          "selected": true,
          "text": "ews-sof-prod",
          "value": "ews-sof-prod"
        },
        {
          "selected": false,
          "text": "ews-dev",
          "value": "ews-dev"
        },
        {
          "selected": false,
          "text": "ews-uat",
          "value": "ews-uat"
        },
        {
          "selected": false,
          "text": "ews-int",
          "value": "ews-int"
        }
      ],
      "query": "",
      "refresh": 0,
      "regex": "",
      "skipUrlSync": false,
      "sort": 0,
      "tagValuesQuery": "",
      "tags": [],
      "tagsQuery": "",
      "type": "custom",
      "useTags": false
    }
'@

    # Check if templating section exists
    if ($JsonContent -match '"templating":\s*\{[^}]*"list":\s*\[') {
        # Check if namespace variable already exists
        if ($JsonContent -match '"name":\s*"namespace"') {
            Write-Host "  Namespace variable already exists, skipping addition..." -ForegroundColor Yellow
            return $JsonContent
        } else {
            Write-Host "  Adding new namespace variable to existing templating list..." -ForegroundColor Green
            # Add to existing list - find the opening bracket of the list and insert after it
            # Check if the list is empty or has content
            if ($JsonContent -match '"templating":\s*\{[^}]*"list":\s*\[\s*\]') {
                # Empty list - add without trailing comma
                $JsonContent = $JsonContent -replace '("templating":\s*\{[^}]*"list":\s*\[)\s*(\])', "`$1`n$namespaceVariableJson`n    `$2"
            } else {
                # Non-empty list - add with trailing comma
                $JsonContent = $JsonContent -replace '("templating":\s*\{[^}]*"list":\s*\[)', "`$1`n$namespaceVariableJson,"
            }
        }
    } else {
        # Check if templating section exists but with empty list
        if ($JsonContent -match '"templating":\s*\{\s*"list":\s*\[\s*\]\s*\}') {
            Write-Host "  Adding namespace variable to empty templating list..." -ForegroundColor Green
            $JsonContent = $JsonContent -replace '("templating":\s*\{\s*"list":\s*\[)\s*(\]\s*\})', "`$1`n$namespaceVariableJson`n  `$2"
        } else {
            Write-Host "  Creating new templating section with namespace variable..." -ForegroundColor Green
            # Add templating section before the closing brace of the main object
            $templatingSection = @"
  "templating": {
    "list": [
$namespaceVariableJson
    ]
  },
"@
            $JsonContent = $JsonContent -replace '(\s*)(}[^}]*$)', "$templatingSection`n`$1`$2"
        }
    }

    return $JsonContent
}

function Replace-HardcodedNamespaces {
    param(
        [string]$JsonContent
    )

    $replacements = 0

    # Define the hardcoded namespace values to replace
    $hardcodedNamespaces = @('ews-sof-prod', 'ews-dev', 'ews-uat', 'ews-int')



    foreach ($namespace in $hardcodedNamespaces) {
        # Replace escaped quotes in JSON (namespace=\"hardcoded-value\")
        # This handles the case where JSON has escaped quotes like: "expr": "metric{namespace=\"ews-sof-prod\"}"
        $pattern1 = 'namespace=\"' + $namespace + '\"'
        $replacement1 = 'namespace=\"$namespace\"'
        $matches1 = [regex]::Matches($JsonContent, [regex]::Escape($pattern1))
        if ($matches1.Count -gt 0) {
            $JsonContent = $JsonContent -replace [regex]::Escape($pattern1), $replacement1
            $replacements += $matches1.Count
            Write-Host "    Replaced $($matches1.Count) occurrences of escaped quotes pattern: $pattern1" -ForegroundColor Gray
        }

        # Replace regex matching with escaped quotes (namespace=~\"hardcoded-value\")
        $pattern2 = 'namespace=~\"' + $namespace + '\"'
        $replacement2 = 'namespace=~\"$namespace\"'
        $matches2 = [regex]::Matches($JsonContent, [regex]::Escape($pattern2))
        if ($matches2.Count -gt 0) {
            $JsonContent = $JsonContent -replace [regex]::Escape($pattern2), $replacement2
            $replacements += $matches2.Count
            Write-Host "    Replaced $($matches2.Count) occurrences of regex pattern: $pattern2" -ForegroundColor Gray
        }

        # Replace literal quotes without escaping (namespace="hardcoded-value") - for raw content
        $pattern3 = 'namespace="' + $namespace + '"'
        $replacement3 = 'namespace="$namespace"'
        $matches3 = [regex]::Matches($JsonContent, [regex]::Escape($pattern3))
        if ($matches3.Count -gt 0) {
            $JsonContent = $JsonContent -replace [regex]::Escape($pattern3), $replacement3
            $replacements += $matches3.Count
            Write-Host "    Replaced $($matches3.Count) occurrences of literal quotes pattern: $pattern3" -ForegroundColor Gray
        }
    }

    return @{
        Content = $JsonContent
        ReplacementCount = $replacements
    }
}

function Process-DashboardFile {
    param(
        [string]$FilePath
    )

    Write-Host "Processing: $FilePath" -ForegroundColor Cyan

    try {
        # Read the JSON file
        $jsonContent = Get-Content $FilePath -Raw -Encoding UTF8

        # Validate that it's valid JSON by trying to parse it
        try {
            $jsonContent | ConvertFrom-Json | Out-Null
        }
        catch {
            Write-Error "Invalid JSON in file $FilePath`: $_"
            return $false
        }

        # Add or update namespace variable (preserving original formatting)
        $updatedJsonContent = Add-NamespaceVariable $jsonContent

        # Replace hardcoded namespace values
        $replacementResult = Replace-HardcodedNamespaces $updatedJsonContent
        $finalJsonContent = $replacementResult.Content
        $replacementCount = $replacementResult.ReplacementCount

        if ($replacementCount -gt 0) {
            Write-Host "  Replaced $replacementCount hardcoded namespace references" -ForegroundColor Green
        } else {
            Write-Host "  No hardcoded namespace references found to replace" -ForegroundColor Yellow
        }

        if (-not $DryRun) {
            # Write the updated content back to the file
            $finalJsonContent | Out-File -FilePath $FilePath -Encoding UTF8 -NoNewline
            Write-Host "  Updated: $FilePath" -ForegroundColor Green
        } else {
            Write-Host "  Would update: $FilePath" -ForegroundColor Gray
        }

        return $true
    }
    catch {
        Write-Error "Error processing $FilePath`: $_"
        return $false
    }
}

# Main execution
Write-Host "Fixing namespace variables in Grafana dashboard files..." -ForegroundColor Magenta
Write-Host "Dry run mode: $DryRun" -ForegroundColor Magenta
Write-Host ""

$processedCount = 0
$successCount = 0

if ($FilePath) {
    # Process single file
    if (Test-Path $FilePath) {
        $processedCount = 1
        if (Process-DashboardFile $FilePath) {
            $successCount = 1
        }
    } else {
        Write-Error "File not found: $FilePath"
        exit 1
    }
} else {
    # Process all dashboard.json files recursively
    $dashboardFiles = Get-ChildItem -Path . -Name "dashboard.json" -Recurse
    
    foreach ($file in $dashboardFiles) {
        $processedCount++
        if (Process-DashboardFile $file) {
            $successCount++
        }
        Write-Host ""
    }
}

Write-Host "Summary:" -ForegroundColor Cyan
Write-Host "Total files processed: $processedCount"
Write-Host "Successfully updated: $successCount"
Write-Host "Failed: $($processedCount - $successCount)"

if ($DryRun) {
    Write-Host ""
    Write-Host "This was a dry run. No files were actually modified." -ForegroundColor Yellow
    Write-Host "Run without -DryRun to apply the changes." -ForegroundColor Yellow
}
